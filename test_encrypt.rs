use std::env;

// Simple test to encrypt the password "npci"
fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Add the src directory to the module path
    let current_dir = env::current_dir()?;
    println!("Current directory: {:?}", current_dir);
    
    // We'll use the crypto module directly
    mod crypto {
        use aes_gcm::{Aes256Gcm, Key, Nonce, KeyInit};
        use aes_gcm::aead::{Aead, OsRng};
        use pbkdf2::pbkdf2_hmac;
        use sha2::Sha256;
        use base64::{Engine as _, engine::general_purpose};
        use rand::RngCore;
        
        pub struct CryptoManager {
            master_key: [u8; 32],
        }
        
        impl CryptoManager {
            pub fn new(master_password: &str) -> Result<Self, Box<dyn std::error::Error>> {
                let salt = b"rustycluster_salt_v1";
                let mut key = [0u8; 32];
                pbkdf2_hmac::<Sha256>(master_password.as_bytes(), salt, 100_000, &mut key);
                Ok(Self { master_key: key })
            }
            
            pub fn encrypt(&self, plaintext: &str) -> Result<String, Box<dyn std::error::Error>> {
                if plaintext.is_empty() {
                    return Ok(String::new());
                }
                
                let mut nonce_bytes = [0u8; 12];
                OsRng.fill_bytes(&mut nonce_bytes);
                let nonce = Nonce::from_slice(&nonce_bytes);
                
                let key = Key::<Aes256Gcm>::from_slice(&self.master_key);
                let cipher = Aes256Gcm::new(key);
                
                let ciphertext = cipher.encrypt(nonce, plaintext.as_bytes())?;
                
                let mut combined = Vec::with_capacity(12 + ciphertext.len());
                combined.extend_from_slice(&nonce_bytes);
                combined.extend_from_slice(&ciphertext);
                
                let encoded = general_purpose::STANDARD.encode(&combined);
                Ok(format!("ENC:{}", encoded))
            }
            
            pub fn decrypt(&self, encrypted_data: &str) -> Result<String, Box<dyn std::error::Error>> {
                if encrypted_data.is_empty() {
                    return Ok(String::new());
                }
                
                if !encrypted_data.starts_with("ENC:") {
                    return Ok(encrypted_data.to_string());
                }
                
                let encoded_data = &encrypted_data[4..];
                let combined = general_purpose::STANDARD.decode(encoded_data)?;
                
                if combined.len() < 13 {
                    return Err("Encrypted data too short".into());
                }
                
                let (nonce_bytes, ciphertext) = combined.split_at(12);
                let nonce = Nonce::from_slice(nonce_bytes);
                
                let key = Key::<Aes256Gcm>::from_slice(&self.master_key);
                let cipher = Aes256Gcm::new(key);
                
                let plaintext = cipher.decrypt(nonce, ciphertext)?;
                Ok(String::from_utf8(plaintext)?)
            }
        }
        
        pub fn create_crypto_manager() -> Result<CryptoManager, Box<dyn std::error::Error>> {
            let master_key = env::var("RUSTYCLUSTER_MASTER_KEY")
                .unwrap_or_else(|_| "rustycluster_default_master_key_change_in_production".to_string());
            CryptoManager::new(&master_key)
        }
    }
    
    let crypto = crypto::create_crypto_manager()?;
    
    // Test encrypting "npci"
    let password = "npci";
    let encrypted = crypto.encrypt(password)?;
    println!("Password '{}' encrypted as: {}", password, encrypted);
    
    // Test decrypting it back
    let decrypted = crypto.decrypt(&encrypted)?;
    println!("Decrypted back to: {}", decrypted);
    
    // Test encrypting "testpass"
    let password2 = "testpass";
    let encrypted2 = crypto.encrypt(password2)?;
    println!("Password '{}' encrypted as: {}", password2, encrypted2);
    
    Ok(())
}
