# RustyCluster Password Encryption Guide

RustyC<PERSON> supports encrypted password storage for enhanced security. This feature allows you to store sensitive credentials (authentication passwords and Redis passwords) in encrypted format within configuration files.

## Overview

The encryption system uses:
- **AES-256-GCM** encryption for maximum security
- **PBKDF2** key derivation with 100,000 iterations
- **Base64 encoding** for storage in configuration files
- **Automatic detection** of encrypted vs plain text passwords
- **Backward compatibility** with existing plain text configurations

## Quick Start

### 1. Set Master Key (Recommended)

Set the master encryption key as an environment variable:

```bash
# Windows (PowerShell)
$env:RUSTYCLUSTER_MASTER_KEY="your_secure_master_key_here"

# Windows (Command Prompt)
set RUSTYCLUSTER_MASTER_KEY=your_secure_master_key_here

# Linux/macOS
export RUSTYCLUSTER_MASTER_KEY="your_secure_master_key_here"
```

**Important:** If you don't set this variable, a default key will be used, which is less secure.

### 2. Encrypt Your Passwords

Use the built-in encryption utility:

```bash
cargo run --bin encrypt_password
```

Example session:
```
RustyCluster Password Encryption Utility
========================================

✓ Using RUSTYCLUSTER_MASTER_KEY environment variable

Enter password to encrypt (or 'quit' to exit): mypassword123

✓ Password encrypted successfully!
Original:  mypassword123
Encrypted: ENC:SGVsbG9Xb3JsZA==

Copy the encrypted value to your configuration file.
```

### 3. Update Configuration Files

Replace plain text passwords with encrypted ones:

```toml
# Before (plain text)
auth_password = "mypassword123"
redis_url = "redis://user:mypassword123@localhost:6379"

# After (encrypted)
auth_password = "ENC:SGVsbG9Xb3JsZA=="
redis_url = "redis://user:ENC:SGVsbG9Xb3JsZA==@localhost:6379"
```

### 4. Run RustyCluster

The application automatically detects and decrypts encrypted passwords:

```bash
cargo run --release config.toml
```

## Supported Password Fields

The following configuration fields support encryption:

### Authentication Passwords
- `auth_password` - Client authentication password

### Redis Connection URLs
- `redis_url` - Primary Redis connection URL
- `peer_redis_nodes` - List of peer Redis URLs for write consistency

### Redis URL Formats

All Redis URL formats are supported:

```toml
# Username and password
redis_url = "redis://user:ENC:encrypted_password@host:port"

# Password only
redis_url = "redis://:ENC:encrypted_password@host:port"

# Username only (treated as password by Redis)
redis_url = "redis://ENC:encrypted_username@host:port"
```

## Utility Commands

### Encrypt Passwords

```bash
cargo run --bin encrypt_password
```

Interactive utility to encrypt passwords for configuration files.

### Decrypt Passwords (Verification)

```bash
cargo run --bin decrypt_password
```

Interactive utility to decrypt and verify encrypted passwords.

## Security Best Practices

### 1. Master Key Management

- **Use unique master keys** for different environments (dev, staging, production)
- **Never commit master keys** to version control
- **Store master keys securely** using:
  - Environment variables
  - Secure key management systems (AWS KMS, Azure Key Vault, etc.)
  - Configuration management tools (Ansible Vault, etc.)

### 2. Key Rotation

To rotate your master key:

1. Generate a new master key
2. Re-encrypt all passwords with the new key
3. Update configuration files
4. Deploy with the new master key

### 3. Environment Separation

Use different master keys for each environment:

```bash
# Development
export RUSTYCLUSTER_MASTER_KEY="dev_key_12345"

# Staging
export RUSTYCLUSTER_MASTER_KEY="staging_key_67890"

# Production
export RUSTYCLUSTER_MASTER_KEY="prod_key_abcdef"
```

## Backward Compatibility

The encryption system is fully backward compatible:

- **Plain text passwords** continue to work without changes
- **Mixed configurations** (some encrypted, some plain text) are supported
- **No breaking changes** to existing deployments

## Troubleshooting

### Common Issues

1. **"Failed to decrypt passwords" error**
   - Ensure `RUSTYCLUSTER_MASTER_KEY` is set correctly
   - Verify you're using the same master key used for encryption

2. **"Invalid format" error**
   - Check that encrypted passwords start with `ENC:`
   - Verify the base64 encoding is not corrupted

3. **Connection failures after encryption**
   - Test decryption using `cargo run --bin decrypt_password`
   - Verify the original password was encrypted correctly

### Debug Mode

Enable debug logging to see encryption/decryption activity:

```bash
RUST_LOG=debug cargo run --release config.toml
```

## Example Configuration

See `config_encrypted_example.toml` for a complete example with encrypted passwords.

## Technical Details

### Encryption Algorithm
- **Cipher:** AES-256-GCM (Galois/Counter Mode)
- **Key Derivation:** PBKDF2-HMAC-SHA256 with 100,000 iterations
- **Nonce:** 96-bit random nonce per encryption
- **Authentication:** Built-in authentication tag (GCM mode)

### Storage Format
```
ENC:base64(nonce || ciphertext || auth_tag)
```

Where:
- `nonce`: 12 bytes (96 bits)
- `ciphertext`: Variable length
- `auth_tag`: 16 bytes (128 bits) - included in GCM mode

### Key Derivation
```
key = PBKDF2-HMAC-SHA256(master_password, salt="rustycluster_salt_v1", iterations=100000)
```

## Migration Guide

### From Plain Text to Encrypted

1. **Backup** your current configuration files
2. **Set** the master key environment variable
3. **Encrypt** each password using the utility
4. **Update** configuration files with encrypted values
5. **Test** the application startup
6. **Verify** all connections work correctly

### Bulk Migration Script

For multiple passwords, you can create a simple script:

```bash
#!/bin/bash
export RUSTYCLUSTER_MASTER_KEY="your_master_key"

echo "password1" | cargo run --bin encrypt_password
echo "password2" | cargo run --bin encrypt_password
echo "password3" | cargo run --bin encrypt_password
```

## Support

If you encounter issues with password encryption:

1. Check the troubleshooting section above
2. Enable debug logging for more details
3. Verify your master key is set correctly
4. Test with the decrypt utility to verify encryption/decryption works
