# Example configuration file with encrypted passwords
# This demonstrates how to use encrypted passwords in RustyCluster

# Redis connection URL with encrypted password
# Original: redis://settlenxt:npci@127.0.0.1:6379
# The password 'npci' has been encrypted using the encrypt_password utility
redis_url = "redis://settlenxt:ENC:base64_encrypted_password_here@127.0.0.1:6379"

# List of secondary node URLs that this node will replicate to (Rust nodes)
secondary_nodes = ["http://127.0.0.1:50052"]

# Port number on which the gRPC server will listen
port = 50051

# Maximum number of connections in the Redis connection pool
redis_pool_size = 1024

# Number of secondary nodes to which data should be replicated
replication_factor = 1

# Number of secondary nodes that must have the data before considering it consistent
read_consistency = 0

# Enable asynchronous replication (required for site replication)
async_replication = true

# Maximum number of retries for connection attempts
max_retries = 3

# Delay between retries in milliseconds
retry_delay_ms = 50

# Maximum age of a replication batch in seconds
replication_batch_max_age_secs = 30

# Connection pool size for secondary nodes
secondary_pool_size = 512

# Maximum number of operations in a batch
max_batch_size = 1000

# Interval in milliseconds to flush batches
batch_flush_interval_ms = 50

# TCP keepalive settings
tcp_keepalive_secs = 30
tcp_nodelay = true

# Concurrency settings
concurrency_limit = 1000
max_concurrent_streams = 2048

# Chunk and shard settings
chunk_size = 1000
num_shards = 16

# Worker threads (auto-detected based on CPU cores)
worker_threads = 8

# Authentication configuration with encrypted password
auth_enabled = true
auth_username = "testuser"
# Original password: testpass
# Encrypted using encrypt_password utility
auth_password = "ENC:base64_encrypted_password_here"
session_duration_secs = 3600
auth_mode = "per_request"
auth_token_expiry_enabled = true

# Write consistency configuration with encrypted Redis passwords
# Original: redis://settlenxt:npci@127.0.0.1:6370
peer_redis_nodes = [
    "redis://settlenxt:ENC:base64_encrypted_password_here@127.0.0.1:6370",
    "redis://settlenxt:ENC:base64_encrypted_password_here@127.0.0.1:6371"
]
write_consistency = "QUORUM"
quorum_value = 2
write_retry_count = 3
peer_redis_pool_size = 64

# Site replication configuration (disabled for this example)
site_replication_enabled = false
site_primary_node = ""
site_failover_node = ""
site_replication_retry_count = 3
site_replication_timeout_ms = 5000
site_replication_pool_size = 256

# Health check configuration
redis_keepalive_enabled = true
redis_keepalive_interval_secs = 30
redis_idle_threshold_secs = 60
secondary_nodes_keepalive_enabled = true
secondary_nodes_keepalive_interval_secs = 30
secondary_nodes_idle_threshold_secs = 60
site_nodes_keepalive_enabled = false
site_nodes_keepalive_interval_secs = 30
site_nodes_idle_threshold_secs = 60
peer_redis_keepalive_enabled = true
peer_redis_keepalive_interval_secs = 30
peer_redis_idle_threshold_secs = 60

# Connection settings
use_physical_connections = true

# Rate limiting configuration (disabled for this example)
rate_limiting_enabled = false
rate_limit_type = "per_client"
rate_limit_requests_per_second = 1000
rate_limit_burst_size = 100
rate_limit_global_requests_per_second = 10000
rate_limit_global_burst_size = 1000
rate_limit_cleanup_interval_secs = 300

# Hot configuration reload settings
config_watch_interval_secs = 5

# Instructions for using encrypted passwords:
# 
# 1. Set the RUSTYCLUSTER_MASTER_KEY environment variable:
#    export RUSTYCLUSTER_MASTER_KEY="your_secure_master_key_here"
#
# 2. Use the encrypt_password utility to encrypt your passwords:
#    cargo run --bin encrypt_password
#
# 3. Replace the placeholder "ENC:base64_encrypted_password_here" values
#    with the actual encrypted passwords from the utility
#
# 4. The application will automatically detect and decrypt encrypted passwords
#    at startup
#
# Security Notes:
# - Keep your master key secure and never commit it to version control
# - Use different master keys for different environments (dev, staging, prod)
# - Encrypted passwords are only as secure as your master key
# - Consider using environment variables or secure key management systems
#   for the master key in production environments
